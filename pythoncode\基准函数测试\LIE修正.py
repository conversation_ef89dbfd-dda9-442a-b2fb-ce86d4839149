# 原始EDV评估函数
def EDV(S, G, p):
    """
    原始影响力评估 (Expected Diffusion Value)
    Args:
        S: 种子节点集合
        G: GEN_GRAPH对象
        p: 传播概率
    Returns:
        float: 估计的影响力值
    """
    S = set(S)
    adj_dict = G.neighbors
    NS_1 = {neighbor for s in S for neighbor in adj_dict.get(s, set())} - S
    influence_sum = 0
    for node in NS_1:
        num_connections = len(set(adj_dict[node]) & S)
        influence_sum += 1 - (1 - p) ** num_connections
    return len(S) + influence_sum

# 原始LIE二跳评估函数
def LIE_two_hop(S, G, p):
    """
    原始二跳LIE影响力评估
    Args:
        S: 种子节点集合
        G: GEN_GRAPH对象
        p: 传播概率
    Returns:
        float: 估计的影响力值
    """
    S = set(S)
    adj_dict = G.neighbors
    neighbor_1st = set()
    for u in S:
        neighbor_1st.update(set(adj_dict[u]) - S)
    neighbor_2nd = set()
    for v in neighbor_1st:
        neighbor_2nd.update(set(adj_dict[v]) - S - neighbor_1st)
    # 一跳
    v_link_count = np.array([len(set(adj_dict[v]) & S) for v in neighbor_1st], dtype=np.float64)
    temp_sum = np.sum(1 - (1 - p) ** v_link_count)
    # 二跳
    num2 = sum(len(set(adj_dict[ss]) & neighbor_1st) for ss in neighbor_2nd)
    temp_sum_2 = (temp_sum * num2 * p) / len(neighbor_1st) if neighbor_1st else 0
    return len(S) + temp_sum + temp_sum_2
from ast import Set
from re import S
import networkx as nx
import numpy as np
import time


class GEN_GRAPH:
    """Network graph generator and neighbor cache container
    
    Attributes:
        network_path (str): 网络数据文件路径（包含节点关系的文本文件）
        nx_G (nx.Graph): NetworkX图对象实例
        neighbors (dict): 节点邻居字典缓存{节点: [直接邻居列表]}
    """
    
    def __init__(self, network_path: str) -> None:
        """初始化图生成器并预生成邻居缓存
        
        Args:
            network_path: 网络数据文件路径（需符合np.loadtxt格式要求）
            
        Raises:
            FileNotFoundError: 当指定路径不存在时
            ValueError: 当文件格式不符合要求时
        """
        self.network_path = network_path
        self.nx_G = self.gen_graph()
        self.neighbors = {node: list(self.nx_G.neighbors(node)) for node in self.nx_G.nodes()}

    def gen_graph(self) -> nx.Graph:
        """从边列表文件构建无向图（支持标准边列表格式）
        
        文件格式要求：
        - 首行为表头（将被自动跳过）
        - 每行包含两个整数节点ID（从第0列和第1列读取）
        
        Returns:
            生成的NetworkX无向图对象
            
        Raises:
            FileNotFoundError: 当文件路径不存在时
            ValueError: 当数据列不足或无法转换为整数时
            RuntimeError: 当图构建过程中出现未知错误时
        """
        try:
            G = nx.Graph()
            # 使用numpy加速边数据加载（跳过首行表头）
            edges_data = np.loadtxt(
                self.network_path,
                skiprows=1,    # 忽略标题行
                usecols=[0, 1] # 仅读取前两列作为边
            )
            # 将浮点数据转换为整数节点ID（适用于非负整数ID）
            edges = [(int(u), int(v)) for u, v in edges_data]
            G.add_edges_from(edges)
            return G
        except FileNotFoundError as e:
            raise FileNotFoundError(f"Network file not found: {self.network_path}") from e
        except ValueError as e:
            raise ValueError(f"Invalid data format in {self.network_path}") from e
        except Exception as e:
            raise RuntimeError(f"Graph construction failed: {str(e)}") from e



def optimized_EDV(graph, S, p):
    """
    优化后的影响力评估 (Expected Diffusion Value)
    
    Args:
        graph: NetworkX图对象
        S: 种子节点集合
        p: 传播概率
        
    Returns:
        float: 估计的影响力值
    """
    # 预生成邻接字典（将邻居存储为集合）
    adj_dict = graph.neighbors
    S = set(S)
    
    # 计算一阶邻居 (NS_1)，直接使用集合操作
    NS_1 = {neighbor for s in S for neighbor in adj_dict.get(s, set())} - S
    
    # 快速计算每个邻居的连接数
    influence_sum = 0
    for node in NS_1:
        num_connections = len(set(adj_dict[node]) & S)
        influence_sum += 1 - (1 - p) ** num_connections
    
    return len(S) + influence_sum





def IC_vec(g, seed, p, mc=1000):
    """向量化实现的独立级联模型模拟
    
    Args:
        g: NetworkX图对象
        seed: 种子节点集合
        p: 传播概率
        mc: 蒙特卡洛模拟次数
        
    Returns:
        float: 平均影响力扩散范围
    """
    # 预处理节点到索引的映射和邻居列表
    node_list = list(g.nodes())
    node_to_index = {node: idx for idx, node in enumerate(node_list)}
    n = len(node_list)
    
    # 预存每个节点的邻居索引数组（向量化预生成）
    preprocessed_neighbors = [
        np.array([node_to_index[n] for n in g.neighbors(node)], dtype=np.int32)
        for node in node_list
    ]
    
    # 转换种子节点为索引
    seed_indices = np.array([node_to_index[n] for n in seed], dtype=np.int32)
    
    # 预分配内存空间
    influence = np.empty(mc, dtype=np.int32)
    
    # 批量生成随机数（优化随机数生成效率）
    rand_pool = np.random.rand(mc, n*5)  # 预生成随机数池（5倍冗余）
    pool_ptr = 0
    
    for i in range(mc):
        active = np.zeros(n, dtype=np.bool_)
        active[seed_indices] = True
        newly_active = active.copy()
        
        while np.any(newly_active):
            # 向量化收集激活节点的所有邻居
            current_active = np.flatnonzero(newly_active)
            neighbors = np.concatenate([preprocessed_neighbors[idx] for idx in current_active])
            
            if neighbors.size == 0:
                break
            
            # 生成与neighbors长度匹配的随机数
            rand_values = np.random.rand(len(neighbors))  # 直接生成所需长度的随机数
            
            # 向量化筛选激活节点
            activated = neighbors[rand_values < p]
            unique_activated = np.unique(activated)
            
            # 更新激活状态
            is_new = ~active[unique_activated]
            newly_active_nodes = unique_activated[is_new]
            
            active[newly_active_nodes] = True
            newly_active.fill(False)
            newly_active[newly_active_nodes] = True
        
        influence[i] = active.sum()
    
    return np.mean(influence)

# 在向量化计算部分使用更高精度的数据类型
def vectorized_newLIE_two_hop_v2(s_set, G, p):
    """
    基于条件概率修正的LIE二跳影响力估计（支持nx.Graph和向量化运算，IC模型均匀概率）
    Args:
        s_set (set): 种子节点集合
        G (nx.Graph): 网络图对象
        p (float): 传播概率 (统一概率)
    Returns:
        float: 影响力估计值
    """
    s_set = set(s_set)
    # 节点到邻居集合的映射（假设G为nx.Graph，adj_dict[node]为可迭代对象）
    adj_dict = G.adj if hasattr(G, 'adj') else G.neighbors

    # 一跳邻居
    neighbor_1st = set()
    for u in s_set:
        neighbor_1st.update(set(adj_dict[u]) - s_set)
    # 二跳邻居（非种子、非一跳）
    neighbor_2nd = set()
    for v in neighbor_1st:
        neighbor_2nd.update(set(adj_dict[v]) - s_set - neighbor_1st)
        
    # 计算一跳激活概率P_v
    # 对每个一跳邻居v，统计与s_set有多少条边，P_v = 1 - (1-p)^num_links
    v_list = list(neighbor_1st)
    v_link_count = np.array([len(set(adj_dict[v]) & s_set) for v in v_list], dtype=np.float64)
    P_v = 1 - np.power(1-p, v_link_count)  # 一跳激活概率向量

    v_idx_map = {v: idx for idx, v in enumerate(v_list)}  # 便于后续索引

    # 计算二跳激活概率P_w（条件概率联合补集）
    P_w = []
    for w in neighbor_2nd:
        v_parents = list(set(adj_dict[w]) & neighbor_1st)  # 所有一跳父节点
        if not v_parents:
            continue
        # 分别查找每个v的P_v
        pvs = np.array([P_v[v_idx_map[v]] for v in v_parents])
        # 二跳传播概率p（同为p），即P_v * p
        pvps = pvs * p
        # P(w未被任一v激活) = prod(1 - P_v*p)
        prod_term = np.prod(1 - pvps)
        P_w.append(1 - prod_term)
    P_w = np.array(P_w) if P_w else np.array([])

    # 总影响力：|S| + 一跳激活概率和 + 二跳激活概率和
    result = len(s_set) + np.sum(P_v) + np.sum(P_w)
    return result


def main():
    network_path = "D:\\VS\\code\\networks\\blog.txt"
    G = GEN_GRAPH(network_path)
    k = 50
    degree_centrality = nx.degree_centrality(G.nx_G)
    sorted_nodes = sorted(degree_centrality.items(), key=lambda x: x[1], reverse=True)
    top_k_nodes = [node for node, _ in sorted_nodes[:k]]

    for p in [0.01, 0.05, 0.1]:
        print(f"\n===== 测试传播概率 p={p} =====")
        result_edv = EDV(top_k_nodes, G, p)
        result_lie = LIE_two_hop(top_k_nodes, G, p)
        result_lie_v2 = vectorized_newLIE_two_hop_v2(top_k_nodes, G.nx_G, p)
        result_ic = IC_vec(G.nx_G, top_k_nodes, p, mc=10000)

        print("EDV:", result_edv)
        print("原始LIE:", result_lie)
        print("修正后LIE:", result_lie_v2)
        print("IC模型模拟 (10000次):", result_ic)
        if result_ic > 0:
            print("EDV相对误差: {:.2f}%".format(abs((result_edv - result_ic)/result_ic)*100))
            print("原始LIE相对误差: {:.2f}%".format(abs((result_lie - result_ic)/result_ic)*100))
            print("修正后LIE相对误差: {:.2f}%".format(abs((result_lie_v2 - result_ic)/result_ic)*100))

if __name__ == "__main__":
    main()